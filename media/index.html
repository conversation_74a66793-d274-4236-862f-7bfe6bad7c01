<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width">
<style>
html, body {
	margin: 0;
	padding: 0;
	height: 100%;
	font-family: 'Arial', sans-serif;
}
#video {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgb(30, 30, 30);
}
#message {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	text-align: center;
	justify-content: center;
	font-size: 16px;
	font-weight: bold;
	color: white;
	pointer-events: none;
	padding: 20px;
	box-sizing: border-box;
	text-shadow: 0 0 5px black;
	flex-direction: column;
	gap: 20px;
}

#reconnect-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border: none;
	border-radius: 12px;
	color: white;
	padding: 6px 12px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
	pointer-events: auto;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	position: relative;
	overflow: hidden;
}

#reconnect-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
	background: linear-gradient(135deg, #7c8ff0 0%, #8a5cb8 100%);
}

#reconnect-btn:active {
	transform: translateY(0);
	box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

#reconnect-btn:disabled {
	background: #555;
	cursor: not-allowed;
	transform: none;
	box-shadow: none;
}

#reconnect-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
	transition: left 0.5s;
}

#reconnect-btn:hover::before {
	left: 100%;
}
</style>
<script defer src="./reader.js"></script>
</head>
<body>

<video id="video"></video>
<div id="message">
  <div id="message-text"></div>
  <button id="reconnect-btn" style="display: none;">重试</button>
</div>

<script>

const video = document.getElementById('video');
const message = document.getElementById('message');
const messageText = document.getElementById('message-text');
const reconnectBtn = document.getElementById('reconnect-btn');
let defaultControls = false;
let currentReader = null;
let errorCount = 0;
let currentConfig = null;
const MAX_ERRORS = 3; // 最大连续错误次数

const setMessage = (str, showReconnectBtn = false) => {
  if (str !== '') {
    video.controls = false;
  } else {
    video.controls = defaultControls;
  }
  messageText.innerText = str;
  reconnectBtn.style.display = showReconnectBtn ? 'block' : 'none';
};

// 重新连接函数 - 直接刷新页面
const reconnect = () => {
  reconnectBtn.disabled = true;
  reconnectBtn.innerText = '重新连接中...';
  setTimeout(() => {
    window.location.reload();
  }, 500);
};

// 绑定重新连接按钮事件
reconnectBtn.addEventListener('click', reconnect);

const parseBoolString = (str, defaultVal) => {
  str = (str || '');

  if (['1', 'yes', 'true'].includes(str.toLowerCase())) {
    return true;
  }
  if (['0', 'no', 'false'].includes(str.toLowerCase())) {
    return false;
  }
  return defaultVal;
};

const loadAttributesFromQuery = () => {
  const params = new URLSearchParams(window.location.search);
  
  video.controls = parseBoolString(params.get('controls'), true);
  video.muted = parseBoolString(params.get('muted'), true);
  video.autoplay = parseBoolString(params.get('autoplay'), true);
  video.playsInline = parseBoolString(params.get('playsinline'), true);
  defaultControls = video.controls;

  return params
};

window.addEventListener('message', (event) => {
  loadAttributesFromQuery();

  const path = event.data.path || ''
  const headers = event.data.headers || {}

  const url = `http://192.168.0.60:8889${path}/whep`

  // 保存当前配置用于重连
  currentConfig = { url, headers };

  // 重置错误计数
  errorCount = 0;

  // 销毁之前的实例
  if (currentReader && typeof currentReader.close === 'function') {
    currentReader.close();
  }

  currentReader = new MediaMTXWebRTCReader({
    url,
    headers,
    onError: (err) => {
      console.error('MediaMTXWebRTCReader error:', err);
      errorCount++;

      if (errorCount >= MAX_ERRORS) {
        // 达到最大错误次数，销毁实例并显示重连按钮
        console.log(`连续错误 ${errorCount} 次，销毁实例`);
        if (currentReader && typeof currentReader.close === 'function') {
          currentReader.close();
        }
        currentReader = null;
        setMessage('连接失败，设备可能已离线', true);
      } else {
        setMessage('已离线');
      }
    },
    onTrack: (evt) => {
      // 连接成功，重置错误计数
      errorCount = 0;
      setMessage('');
      video.srcObject = evt.streams[0];
    },
  });
});
</script>

</body>
</html>
